struct Solution;

impl Solution {
    pub fn is_match(s: String, p: String) -> bool {
        let s_chars: Vec<char> = s.chars().collect();
        let p_chars: Vec<char> = p.chars().collect();
        let s_len = s_chars.len();
        let p_len = p_chars.len();
        
        // dp[i][j] 表示 s 的前 i 个字符是否能被 p 的前 j 个字符匹配
        let mut dp = vec![vec![false; p_len + 1]; s_len + 1];
        
        // 初始化：空字符串匹配空模式
        dp[0][0] = true;
        
        // 初始化第一行：空字符串匹配模式
        // 只有当模式是 "a*b*c*" 这种形式时，空字符串才能匹配
        for j in 2..=p_len {
            if p_chars[j - 1] == '*' {
                dp[0][j] = dp[0][j - 2];
            }
        }
        
        // 填充 dp 表
        for i in 1..=s_len {
            for j in 1..=p_len {
                let s_char = s_chars[i - 1];
                let p_char = p_chars[j - 1];
                
                if p_char == '*' {
                    // 当前模式字符是 '*'
                    // 情况1：匹配0个前面的字符，即忽略 "x*" 这个组合
                    dp[i][j] = dp[i][j - 2];
                    
                    // 情况2：匹配1个或多个前面的字符
                    // 需要检查前面的字符是否能匹配当前字符串字符
                    if j >= 2 {
                        let prev_p_char = p_chars[j - 2];
                        if prev_p_char == '.' || prev_p_char == s_char {
                            dp[i][j] = dp[i][j] || dp[i - 1][j];
                        }
                    }
                } else {
                    // 当前模式字符不是 '*'
                    if p_char == '.' || p_char == s_char {
                        dp[i][j] = dp[i - 1][j - 1];
                    }
                    // 如果字符不匹配，dp[i][j] 保持 false（默认值）
                }
            }
        }
        
        dp[s_len][p_len]
    }
}

fn main() {
    let test_cases = vec![
        ("aa", "a", false),
        ("aa", "a*", true),
        ("ab", ".*", true),
        ("aab", "c*a*b", true),
        ("mississippi", "mis*is*p*.", false),
        ("", ".*", true),
        ("", "", true),
        ("a", "", false),
        ("", "a*", true),
        ("ab", ".*c", false),
        ("aaa", "a*a", true),
        ("aaa", "ab*a*c*a", true),
    ];
    
    println!("正则表达式匹配测试结果：");
    println!("========================");
    
    for (i, (s, p, expected)) in test_cases.iter().enumerate() {
        let result = Solution::is_match(s.to_string(), p.to_string());
        let status = if result == *expected { "✓" } else { "✗" };
        
        println!("测试 {}: {} s=\"{}\" p=\"{}\" 期望={} 实际={}", 
                 i + 1, status, s, p, expected, result);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_basic_cases() {
        assert_eq!(Solution::is_match("aa".to_string(), "a".to_string()), false);
        assert_eq!(Solution::is_match("aa".to_string(), "a*".to_string()), true);
        assert_eq!(Solution::is_match("ab".to_string(), ".*".to_string()), true);
    }
    
    #[test]
    fn test_complex_cases() {
        assert_eq!(Solution::is_match("aab".to_string(), "c*a*b".to_string()), true);
        assert_eq!(Solution::is_match("mississippi".to_string(), "mis*is*p*.".to_string()), false);
    }
    
    #[test]
    fn test_edge_cases() {
        assert_eq!(Solution::is_match("".to_string(), ".*".to_string()), true);
        assert_eq!(Solution::is_match("".to_string(), "".to_string()), true);
        assert_eq!(Solution::is_match("a".to_string(), "".to_string()), false);
        assert_eq!(Solution::is_match("".to_string(), "a*".to_string()), true);
    }
    
    #[test]
    fn test_star_patterns() {
        assert_eq!(Solution::is_match("ab".to_string(), ".*c".to_string()), false);
        assert_eq!(Solution::is_match("aaa".to_string(), "a*a".to_string()), true);
        assert_eq!(Solution::is_match("aaa".to_string(), "ab*a*c*a".to_string()), true);
    }
}
