fn maximal_square(matrix: Vec<Vec<char>>) -> i32 {
    if matrix.is_empty() || matrix[0].is_empty() {
        return 0;
    }
    
    let m = matrix.len();
    let n = matrix[0].len();
    let mut dp = vec![vec![0; n]; m];
    let mut max_side = 0;
    
    for i in 0..m {
        for j in 0..n {
            if matrix[i][j] == '1' {
                if i == 0 || j == 0 {
                    dp[i][j] = 1;
                } else {
                    dp[i][j] = std::cmp::min(
                        std::cmp::min(dp[i-1][j], dp[i][j-1]),
                        dp[i-1][j-1]
                    ) + 1;
                }
                max_side = std::cmp::max(max_side, dp[i][j]);
            }
        }
    }
    
    max_side * max_side
}

fn main() {
    let matrix1 = vec![
        vec!['1','0','1','0','0'],
        vec!['1','0','1','1','1'],
        vec!['1','1','1','1','1'],
        vec!['1','0','0','1','0']
    ];
    println!("示例1结果: {}", maximal_square(matrix1)); // 应输出 4
    
    let matrix2 = vec![
        vec!['0','1'],
        vec!['1','0']
    ];
    println!("示例2结果: {}", maximal_square(matrix2)); // 应输出 1
    
    let matrix3 = vec![vec!['0']];
    println!("示例3结果: {}", maximal_square(matrix3)); // 应输出 0
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_example1() {
        let matrix = vec![
            vec!['1','0','1','0','0'],
            vec!['1','0','1','1','1'],
            vec!['1','1','1','1','1'],
            vec!['1','0','0','1','0']
        ];
        assert_eq!(maximal_square(matrix), 4);
    }

    #[test]
    fn test_example2() {
        let matrix = vec![
            vec!['0','1'],
            vec!['1','0']
    ];
        assert_eq!(maximal_square(matrix), 1);
    }

    #[test]
    fn test_example3() {
        let matrix = vec![vec!['0']];
        assert_eq!(maximal_square(matrix), 0);
    }

    #[test]
    fn test_empty_matrix() {
        let matrix: Vec<Vec<char>> = vec![];
        assert_eq!(maximal_square(matrix), 0);
    }
}
