struct Solution;

impl Solution {
    pub fn longest_palindrome(s: String) -> String {
        let chars: Vec<char> = s.chars().collect();
        let n = chars.len();
        
        if n == 0 {
            return String::new();
        }
        
        let mut start = 0;
        let mut max_len = 1;
        
        for i in 0..n {
            // 检查以 i 为中心的奇数长度回文
            let len1 = Self::expand_around_center(&chars, i as i32, i as i32);
            // 检查以 i 和 i+1 为中心的偶数长度回文  
            let len2 = Self::expand_around_center(&chars, i as i32, (i + 1) as i32);
            
            let len = len1.max(len2);
            
            if len > max_len {
                max_len = len;
                // 计算回文串的起始位置
                start = i - (len - 1) / 2;
            }
        }
        
        chars[start..start + max_len].iter().collect()
    }
    
    fn expand_around_center(chars: &[char], mut left: i32, mut right: i32) -> usize {
        let n = chars.len() as i32;
        
        // 向两边扩散
        while left >= 0 && right < n && chars[left as usize] == chars[right as usize] {
            left -= 1;
            right += 1;
        }
        
        // 返回回文串长度 (right - left - 1)
        // 因为上面的循环结束时，left 和 right 都已经越过了回文的边界
        (right - left - 1) as usize
    }
}

fn main() {
    let test_cases = vec![
        "babad",
        "cbbd",
        "a",
        "ac",
        "racecar",
        "noon",
        "abcdef",
    ];
    
    for test in test_cases {
        let result = Solution::longest_palindrome(test.to_string());
        println!("Input: \"{}\" -> Output: \"{}\"", test, result);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_longest_palindrome() {
        assert_eq!(Solution::longest_palindrome("babad".to_string()), "bab");
        assert_eq!(Solution::longest_palindrome("cbbd".to_string()), "bb");
        assert_eq!(Solution::longest_palindrome("a".to_string()), "a");
        assert_eq!(Solution::longest_palindrome("ac".to_string()), "a");
        assert_eq!(Solution::longest_palindrome("racecar".to_string()), "racecar");
    }
}